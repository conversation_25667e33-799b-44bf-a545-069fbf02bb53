import { create } from "zustand";
import { devtools } from "zustand/middleware";
import type { BusinessItemStore, BusinessItemDetail } from "@/types/BusinessSection.types";

// Mock data for business item details
const mockItemDetails: BusinessItemDetail[] = [
  {
    id: "1",
    title: "Develop AI-powered customer recommendation engine",
    status: "confirmed",
    actions: "Researched existing AI frameworks, built prototype using TensorFlow, tested with sample customer data, integrated with main platform",
    result: "Increased customer engagement by 35% and average order value by 22%. Users spend 40% more time browsing recommended products.",
    description: "An intelligent system that analyzes customer behavior and purchase history to suggest relevant products",
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-20T14:30:00Z"
  },
  {
    id: "2",
    title: "Implement real-time chat support feature",
    status: "action",
    actions: "Evaluated chat platforms (Intercom, Zendesk), designed UI mockups, started backend integration, training support team",
    result: "",
    description: "Live chat functionality to provide instant customer support",
    createdAt: "2024-01-10T09:15:00Z",
    updatedAt: "2024-01-18T16:45:00Z"
  },
  {
    id: "3",
    title: "Create mobile-first responsive design",
    status: "idea",
    actions: "",
    result: "",
    description: "Redesign the platform to work seamlessly on mobile devices",
    createdAt: "2024-01-12T11:30:00Z",
    updatedAt: "2024-01-12T11:30:00Z"
  },
  {
    id: "4",
    title: "Launch referral program for existing customers",
    status: "idea",
    actions: "",
    result: "",
    description: "Incentivize existing customers to refer new users through rewards",
    createdAt: "2024-01-14T13:20:00Z",
    updatedAt: "2024-01-14T13:20:00Z"
  },
  {
    id: "5",
    title: "Optimize page load speeds across platform",
    status: "unproven",
    actions: "Implemented image compression, minified CSS/JS files, set up CDN, added lazy loading for images",
    result: "Reduced average page load time from 4.2s to 2.1s, but bounce rate only improved by 8% instead of expected 25%",
    description: "Technical improvements to enhance user experience through faster loading",
    createdAt: "2024-01-08T14:45:00Z",
    updatedAt: "2024-01-16T10:20:00Z"
  }
];

export const useBusinessItemStore = create<BusinessItemStore>()(
  devtools(
    (set) => ({
      selectedItem: null,
      itemDetails: [],
      isLoading: false,
      error: null,

      setSelectedItem: (item) => set({ selectedItem: item }),
      
      setItemDetails: (details) => set({ itemDetails: details }),
      
      setLoading: (isLoading) => set({ isLoading }),
      
      setError: (error) => set({ error }),
      
      addItemDetail: (detail) => 
        set((state) => ({ 
          itemDetails: [...state.itemDetails, detail] 
        })),
      
      updateItemDetail: (id, updates) =>
        set((state) => ({
          itemDetails: state.itemDetails.map((item) =>
            item.id === id ? { ...item, ...updates } : item
          ),
        })),
      
      removeItemDetail: (id) =>
        set((state) => ({
          itemDetails: state.itemDetails.filter((item) => item.id !== id),
        })),
    }),
    {
      name: "business-item-storage",
    }
  )
);

// Mock function to simulate fetching item details by business item ID
export const fetchItemDetails = async (businessItemId: string): Promise<BusinessItemDetail[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  // Return mock data - in real app, this would filter by businessItemId
  return mockItemDetails;
};
