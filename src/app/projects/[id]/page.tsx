"use client";

import { ProtectedRoute } from "@/components/auth/protected-route";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  useSidebar,
} from "@/components/ui/sidebar";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { ArrowLeft, PanelLeft, PanelLeftClose, User, Bell, FileText, Plus, X } from "lucide-react";
import { ProjectSidebar } from "../../../components/project-sidebar";
import { BusinessSectionsGrid } from "../../../components/business-sections/BusinessSectionsGrid";
import { useBusinessSectionStore } from "../../../stores/businessSectionStore";
import { useBusinessItemStore } from "../../../stores/businessItemStore";
import { BusinessItemTable } from "../../../components/business-item-table";
import { fetchBusinessSections } from "../../../lib/businessSectionsData";
import { SidebarButton } from "../../../components/ui/sidebar-button";
import { ICON_SIZES } from "../../../lib/constants";
import { useResizable } from "../../../hooks/useResizable";
import { useAnalytics } from "../../../hooks/useAnalytics";
import { ThemeToggle } from "../../../components/theme-toggle";

// Header component that uses useSidebar hook
function ProjectHeader({
  activeContent,
  setActiveContent,
  mockDraftItems,
  mockFileItems,
  selectedBusinessItem,
  onBackToItems
}: {
  activeContent: 'drafts' | 'files' | null;
  setActiveContent: (content: 'drafts' | 'files' | null) => void;
  mockDraftItems: any[];
  mockFileItems: any[];
  selectedBusinessItem: any;
  onBackToItems: () => void;
}) {
  const router = useRouter();
  const { toggleSidebar, open } = useSidebar();
  const { trackClick, trackCustomEvent } = useAnalytics();

  return (
    <header className="flex h-20 shrink-0 items-center gap-2 transition-[width] ease-linear border-b border-border">
      <div className="flex items-center justify-between w-full h-full px-4">
        {/* Left side - Navigation */}
        <div className="flex items-center gap-2 h-full ">
          <SidebarButton
            icon={open ? PanelLeftClose : PanelLeft}
            variant="ghost"
            size="lg"
            layout="icon-only"
            hoverColor="grey"
            hoverScale={true}
            showBorder={true}
            onClick={() => {
              trackClick("sidebar-toggle", "project-header");
              trackCustomEvent("sidebar_toggled", {
                from_state: open ? "open" : "closed",
                to_state: open ? "closed" : "open",
                location: "project-header"
              });
              toggleSidebar();
            }}
            iconClassName={ICON_SIZES.lg}
          />
          {selectedBusinessItem && (
            <SidebarButton
              icon={ArrowLeft}
              text="Back to All Items"
              variant="ghost"
              size="md"
              layout="horizontal"
              hoverColor="grey"
              hoverScale={true}
              showBorder={true}
              onClick={() => {
                trackClick("back-to-items", "project-header");
                trackCustomEvent("navigation_back_to_items", {
                  from_item: selectedBusinessItem?.title,
                  location: "header"
                });
                onBackToItems();
              }}
              iconClassName={ICON_SIZES.lg}
            />
          )} 
        </div>

        {/* Right side - Drafts, Files, Notifications, and Profile */}
        <div className="flex items-center gap-2 h-full">
        <ThemeToggle />
          <SidebarButton
            onClick={() => {
              trackClick("drafts-button", "project-header");
              trackCustomEvent("content_section_opened", {
                section: "drafts",
                location: "project-header"
              });
              setActiveContent('drafts');
            }}
            icon={FileText}
            variant="ghost"
            size="lg"
            layout="icon-only"
            showBorder={true}
            hoverColor="grey"
            hoverScale={true}
            iconClassName={ICON_SIZES.lg}
          />
          <SidebarButton
            icon={Bell}
            badge={3}
            variant="ghost"
            size="lg"
            layout="icon-only"
            showBorder={true}
            hoverColor="grey"
            hoverScale={true}
            onClick={() => {
              trackClick("notifications-button", "project-header");
              trackCustomEvent("notifications_opened", {
                notification_count: 3,
                location: "project-header"
              });
              /* Handle notifications */
            }}
            iconClassName={ICON_SIZES.lg}
          />
          <SidebarButton
            icon={User}
            variant="ghost"
            size="lg"
            layout="icon-only"
            showBorder={true}
            hoverColor="grey"
            hoverScale={true}
            onClick={() => {
              trackClick("profile-button", "project-header");
              trackCustomEvent("navigation_clicked", {
                destination: "profile",
                from_page: "project-detail",
                location: "header"
              });
              router.push('/profile');
            }}
            iconClassName={ICON_SIZES.lg}
          />
        </div>
      </div>
    </header>
  );
}

// Mock draft items data
const mockDraftItems = [
  { id: 1, title: "Project proposal", status: "draft", lastModified: "2 hours ago" },
  { id: 2, title: "Design brief", status: "draft", lastModified: "1 day ago" },
  { id: 3, title: "Technical specs", status: "draft", lastModified: "3 days ago" },
];

// Mock file items data
const mockFileItems = [
  { id: 1, title: "logo.svg", type: "image", size: "24KB" },
  { id: 2, title: "wireframes.fig", type: "design", size: "1.2MB" },
  { id: 3, title: "requirements.pdf", type: "document", size: "156KB" },
  { id: 4, title: "styleguide.pdf", type: "document", size: "2.1MB" },
];



export default function ProjectDetailPage() {
  const params = useParams();
  const projectId = params.id;
  const [activeContent, setActiveContent] = useState<'drafts' | 'files' | null>(null);
  const [chatWidth, setChatWidth] = useState<'30%' | '50%'>('30%');
  const [isChatCollapsed, setIsChatCollapsed] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState('30vw');

  // Sync sidebar width with chat width
  const handleChatWidthChange = (width: '30%' | '50%') => {
    setChatWidth(width);
    setSidebarWidth(width === '30%' ? '30vw' : '50vw');
  };

  const { sections, isLoading, error, setSections, setLoading, setError } = useBusinessSectionStore();
  const { selectedItem, itemDetails, setSelectedItem, setItemDetails } = useBusinessItemStore();
  const { trackClick, trackCustomEvent } = useAnalytics();


  // Load business sections on mount
  useEffect(() => {
    const loadBusinessSections = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await fetchBusinessSections(projectId as string);
        setSections(data);
      } catch (err) {
        setError('Failed to load business sections');
        console.error('Error loading business sections:', err);
      } finally {
        setLoading(false);
      }
    };

    loadBusinessSections();
  }, [setSections, setLoading, setError]);

  // Handle business item selection
  const handleBusinessItemClick = (item: any) => {
    setSelectedItem(item);
    
    // Load mock item details for the selected business item
    const loadItemDetails = async () => {
      try {
        const { fetchItemDetails } = await import('@/stores/businessItemStore');
        const details = await fetchItemDetails(item.id);
        setItemDetails(details);
      } catch (err) {
        console.error('Error loading item details:', err);
        setItemDetails([]);
      }
    };
      
    loadItemDetails();
  };

  // Handle back to items
  const handleBackToItems = () => {
    setSelectedItem(null);
  };

  // Resize functionality
  const resizable = useResizable({
    initialWidth: sidebarWidth,
    minWidthPercent: 10,
    maxWidthPercent: 50,
    onWidthChange: (width) => {
      // Convert percentage to viewport width
      const widthPercent = parseFloat(width.replace('%', ''));
      const vwWidth = `${widthPercent}vw`;
      setSidebarWidth(vwWidth);

      // Update chatWidth to match sidebarWidth for consistency
      if (widthPercent <= 30) {
        setChatWidth('30%');
      } else {
        setChatWidth('50%');
      }
    },
    onCollapse: () => {
      setIsChatCollapsed(true);
    },
  });

  return (
    <ProtectedRoute>
      <div className="h-screen overflow-hidden">
        <SidebarProvider
          defaultOpen={true}
          style={{
            '--sidebar-width': sidebarWidth,
            '--sidebar-width-mobile': '18rem',
            '--sidebar-width-icon': '5rem',
          } as React.CSSProperties}
        >
          <ProjectSidebar
            projectId={projectId as string}
            chatWidth={chatWidth}
            setChatWidth={handleChatWidthChange}
            isChatCollapsed={isChatCollapsed}
            setIsChatCollapsed={setIsChatCollapsed}
            selectedBusinessItem={selectedItem}
            resizeHandle={{
              onMouseDown: resizable.handleMouseDown,
              isDragging: resizable.isDragging,
            }}
          />
          <SidebarInset className="flex-1 flex flex-col h-screen overflow-hidden">
            <ProjectHeader
              activeContent={activeContent}
              setActiveContent={setActiveContent}
              mockDraftItems={mockDraftItems}
              mockFileItems={mockFileItems}
              selectedBusinessItem={selectedItem}
              onBackToItems={handleBackToItems}
            />

            {/* Main Content Area */}
            <div className="flex-1 min-w-0 min-h-0 w-full max-w-full p-4 overflow-y-auto bg-gray-50 dark:bg-gray-900 relative project-main-content">


              {/* Content Section - Drafts */}
              {activeContent === 'drafts' && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <SidebarButton
                        icon={X}
                        variant="ghost"
                        size="lg"
                        layout="icon-only"
                        showBorder={true}
                        hoverColor="grey"
                        borderClassName="border-1"
                        hoverScale={true}
                        onClick={() => setActiveContent(null)}
                      >
                      </SidebarButton>
                      <h2 className="text-2xl font-bold text-gray-900">Drafts</h2>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="text-sm text-gray-500">{mockDraftItems.length} drafts</div>
                      <SidebarButton
                        icon={Plus}
                        variant="ghost"
                        size="lg"
                        showBorder={true}
                        
                        borderClassName="border-1"
                        hoverColor="grey"
                        hoverScale={true}
                      >
                        New Draft
                      </SidebarButton>
                    </div>
                  </div>
                  <div className="grid gap-4">
                    {mockDraftItems.map((draft) => (
                      <div key={draft.id} className="p-6 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-all cursor-pointer">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-semibold text-gray-900 text-lg">{draft.title}</h3>
                            <p className="text-sm text-gray-500 mt-1">Modified {draft.lastModified}</p>
                          </div>
                          <div className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                            {draft.status}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Content Section - Files */}
              {activeContent === 'files' && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <button
                        onClick={() => setActiveContent(null)}
                        className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                      <h2 className="text-2xl font-bold text-gray-900">Files</h2>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="text-sm text-gray-500">{mockFileItems.length} files</div>
                      <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        New File
                      </Button>
                    </div>
                  </div>
                  <div className="grid gap-4">
                    {mockFileItems.map((file) => (
                      <div key={file.id} className="p-6 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-all cursor-pointer">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                              <span className="text-gray-500 font-semibold text-sm">
                                {file.type === 'image' ? '🖼️' : file.type === 'design' ? '🎨' : '📄'}
                              </span>
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900 text-lg">{file.title}</h3>
                              <p className="text-sm text-gray-500 mt-1">{file.size} • {file.type}</p>
                            </div>
                          </div>
                          <div className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                            {file.type}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Business Sections Grid - Default View */}
              {!activeContent && !selectedItem && (
                <>
                  {/* Loading State */}
                  {isLoading && (
                    <div className="flex items-center justify-center h-64">
                      <div className="text-center">
                        <div className={`animate-spin rounded-full ${ICON_SIZES.lg} border-b-2 border-gray-900 mx-auto mb-4`}></div>
                        <p className="text-gray-600">Loading business sections...</p>
                      </div>
                    </div>
                  )}

                  {/* Error State */}
                  {error && (
                    <div className="flex items-center justify-center h-64">
                      <div className="text-center">
                        <p className="text-red-600 mb-4">{error}</p>
                        <Button onClick={() => window.location.reload()}>
                          Try Again
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Business Sections Grid */}
                  {!isLoading && !error && sections.length > 0 && (
                    <BusinessSectionsGrid
                      sections={sections}
                      onItemClick={handleBusinessItemClick}
                    />
                  )}

                  {/* Empty State */}
                  {!isLoading && !error && sections.length === 0 && (
                    <div className="flex items-center justify-center h-64">
                      <div className="text-center">
                        <p className="text-gray-600 mb-4">No business sections found</p>
                        <Button onClick={() => window.location.reload()}>
                          Reload
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}

              {/* Business Item Detail Table */}
              {!activeContent && selectedItem && (
                <BusinessItemTable
                  itemDetails={itemDetails}
                  selectedBusinessItem={selectedItem}
                  onBackToItems={handleBackToItems}
                />
              )}
            </div>
          </SidebarInset>
        </SidebarProvider>
      </div>
    </ProtectedRoute>
  );
}
