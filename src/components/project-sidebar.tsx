"use client";

import { <PERSON>S<PERSON><PERSON>, <PERSON>older<PERSON>pen, Zap, Eye, Edit, Trash2, MoreVertical, Home, Plus, Settings, User, LogOut, MessageCircle, ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import * as React from "react";


import { useSidebar } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { ProjectChatSidebar } from "./project-chat-sidebar";
import { SidebarButton } from "./ui/sidebar-button";
import { ResizeHandle } from "./ui/resize-handle";
import { ICON_SIZES } from "@/lib/constants";
import { useAnalytics } from "@/hooks/useAnalytics";

// Mock projects data
const mockProjects = [
  { id: "1", name: "Website Redesign", status: "in-progress" },
  { id: "2", name: "Mobile App", status: "planning" },
  { id: "3", name: "Marketing Campaign", status: "completed" },
  { id: "4", name: "API Integration", status: "in-progress" },
];

// Mock action items data
const mockActionItems = [
  { id: 1, title: "Research competitors", status: "pending", priority: "high" },
  { id: 2, title: "Create wireframes", status: "in-progress", priority: "high" },
  { id: 3, title: "Set up analytics", status: "completed", priority: "medium" },
  { id: 4, title: "Design logo", status: "pending", priority: "medium" },
  { id: 5, title: "Write content", status: "pending", priority: "low" },
];

interface ProjectSidebarProps extends React.ComponentProps<typeof Sidebar> {
  projectId: string;
  chatWidth?: '30%' | '50%';
  setChatWidth?: (width: '30%' | '50%') => void;
  isChatCollapsed?: boolean;
  setIsChatCollapsed?: (collapsed: boolean) => void;
  selectedBusinessItem?: any;
  resizeHandle?: {
    onMouseDown: (e: React.MouseEvent) => void;
    isDragging: boolean;
  };
}

export function ProjectSidebar({
  projectId,
  chatWidth,
  setChatWidth,
  isChatCollapsed,
  setIsChatCollapsed,
  selectedBusinessItem,
  resizeHandle,
  ...props
}: ProjectSidebarProps) {
  const { setOpen, state } = useSidebar();
  const { trackClick, trackCustomEvent } = useAnalytics();
  const router = useRouter();

  const [isProjectDropdownOpen, setIsProjectDropdownOpen] = React.useState(false);
  const [isActionDropdownOpen, setIsActionDropdownOpen] = React.useState(false);

  const activeProject = mockProjects.find(p => p.id === projectId) || mockProjects[0];
  const isCollapsed = state === "collapsed";


  const handleProjectChange = (newProjectId: string) => {
    const selectedProject = mockProjects.find(p => p.id === newProjectId);
    trackClick("project-selector", "project-sidebar");
    trackCustomEvent("project_switched", {
      from_project_id: projectId,
      to_project_id: newProjectId,
      project_name: selectedProject?.name,
      project_status: selectedProject?.status,
      location: "sidebar"
    });
    router.push(`/projects/${newProjectId}`);
    setIsProjectDropdownOpen(false);
  };

  const handleNavigation = (path: string) => {
    trackClick("navigation-link", "project-sidebar");
    trackCustomEvent("navigation_clicked", {
      destination: path,
      from_page: "project-detail",
      location: "sidebar"
    });
    router.push(path);
    setIsProjectDropdownOpen(false);
  };

  const handleProjectIconClick = () => {
    trackClick("project-icon", "project-sidebar");
    trackCustomEvent("sidebar_icon_clicked", {
      icon_type: "project",
      sidebar_state: isCollapsed ? "collapsed" : "expanded",
      action: isCollapsed ? "expand_sidebar" : "toggle_project_dropdown"
    });

    if (isCollapsed) {
      setOpen(true);
    } else {
      setIsProjectDropdownOpen(!isProjectDropdownOpen);
    }
  };

  const handleActionIconClick = () => {
    trackClick("action-icon", "project-sidebar");
    trackCustomEvent("sidebar_icon_clicked", {
      icon_type: "action",
      sidebar_state: isCollapsed ? "collapsed" : "expanded",
      action: isCollapsed ? "expand_sidebar" : "toggle_action_dropdown",
      action_count: mockActionItems.length
    });

    if (isCollapsed) {
      setOpen(true);
    } else {
      setIsActionDropdownOpen(!isActionDropdownOpen);
    }
  };

  return (
    <>
      {/* Backdrop overlay - only show when dropdowns are open */}
      {(isProjectDropdownOpen || isActionDropdownOpen) && (
        <div
          className="fixed inset-0 bg-black/10 backdrop-blur-[0.2px] z-40 transition-all duration-300"
          onClick={() => {
            setIsProjectDropdownOpen(false);
            setIsActionDropdownOpen(false);
          }}
        />
      )}

      <Sidebar
        collapsible="icon"
        className={`border-r flex flex-col h-screen relative overflow-hidden ${isCollapsed ? 'pt-2' : ''}`}
        {...props}
      >
      <SidebarHeader className={isCollapsed ? "h-20 px-4 py-4 flex items-center" : "h-20 px-4 py-4 flex items-center"}>
        {isCollapsed ? (
          /* Collapsed state - show icons vertically */
          <div className="flex flex-col items-center justify-center gap-4 w-full h-full py-11">
            {/* Project Icon */}
             
            <SidebarButton
              onClick={() => {
                trackClick("back-to-dashboard", "project-header");
                trackCustomEvent("navigation_clicked", {
                  destination: "dashboard",
                  from_page: "project-detail",
                  location: "header"
                });
                router.push('/user-dashboard');
              }}
              icon={Home}
              variant="ghost"
              size="lg"
              hoverColor="grey"
              layout="icon-only"
              borderClassName="border-gray-1 border-1"
              showBorder={true}
              hoverScale={true}
              iconClassName={ICON_SIZES.lg}
            />

            {/* Action Icon */}
            <SidebarButton
              onClick={handleActionIconClick}
              icon={Zap}
              badge={mockActionItems.length}
              variant="ghost"
              size="lg"
              layout="icon-only"
              showBorder={true}
              hoverColor="green"
              hoverScale={true}
              className="relative bg-green-100 hover:bg-green-600 text-green-700 hover:text-white border-green-500"
              iconClassName={ICON_SIZES.lg}
            />
          </div>
        ) : (
          /* Expanded state - show full dropdowns */
          <div className="flex items-center gap-2 h-full w-full justify-between">
            {/* Project Selector */}
           <SidebarButton
              onClick={() => {
                trackClick("back-to-dashboard", "project-header");
                trackCustomEvent("navigation_clicked", {
                  destination: "dashboard",
                  from_page: "project-detail",
                  location: "header"
                });
                router.push('/user-dashboard');
              }}
              icon={Home}
              variant="ghost"
              size="lg"
              hoverColor="grey"
              layout="icon-only"
              borderClassName="border-1"
              showBorder={true}
              hoverScale={true}
              iconClassName={ICON_SIZES.lg}
            />

          {/* Actions Button */}
          <DropdownMenu open={isActionDropdownOpen} onOpenChange={setIsActionDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <SidebarButton
                text="Priorities"
                badge={mockActionItems.length}
                variant="ghost"
                layout="horizontal"
                hoverColor="green"
                hoverScale={true}
                showBorder={true}
                className="bg-green-100 hover:bg-green-600 text-green-700  hover:text-white border-green-500 border-2"
              />
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-80 rounded-lg animate-in fade-in-0 zoom-in-95"
              align="end"
              side="bottom"
              sideOffset={4}
            >
              <DropdownMenuLabel className="text-muted-foreground text-xs">
                Project Actions ({mockActionItems.length})
              </DropdownMenuLabel>
              {mockActionItems.map((action) => (
                <DropdownMenuItem
                  key={action.id}
                  className="gap-2 p-3 m-1 border border-transparent transition-colors duration-200 hover:bg-gray-100 hover:border-gray-200 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:border-gray-700 dark:hover:text-gray-100 cursor-pointer rounded-md"
                >
                  <div className="flex size-8 items-center justify-center rounded-md border border-yellow-500 bg-yellow-50">
                    <Zap className={`${ICON_SIZES.md} shrink-0 text-yellow-500`} />
                  </div>
                  <div className="flex flex-col flex-1">
                    <span className="font-medium">{action.priority.toUpperCase()}</span>
                    <span className="text-xs text-muted-foreground capitalize">{action.title}</span>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <button className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800 rounded transition-colors duration-200 flex items-center justify-center">
                        <MoreVertical className={ICON_SIZES.md} />
                      </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-40">
                      <DropdownMenuItem className="gap-2 cursor-pointer hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100">
                        <Eye className={ICON_SIZES.md} />
                        View
                      </DropdownMenuItem>
                      <DropdownMenuItem className="gap-2 cursor-pointer hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100">
                        <Edit className={ICON_SIZES.md} />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem className="gap-2 text-red-600 cursor-pointer hover:bg-red-50 hover:text-red-700 dark:hover:bg-red-950 dark:hover:text-red-300 focus:bg-red-50 focus:text-red-700 dark:focus:bg-red-950 dark:focus:text-red-300">
                        <Trash2 className={ICON_SIZES.md} />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>


          </div>
        )}
      </SidebarHeader>

      {/* Chat Section - Fills remaining height */}
      <SidebarContent className="flex-1 min-h-0 flex flex-col w-full">
        {/* Chat Button - Only when collapsed */}
        {isCollapsed && (
          <div className="flex-shrink-0 mt-auto mb-4 flex justify-center">
            <SidebarButton
              icon={MessageCircle}
              variant="secondary"
              size="lg"
              layout="icon-only"
              showBorder={false}
              hoverColor="green"
              hoverScale={true}
              onClick={() => {
                trackClick("chat-expand-button", "project-sidebar");
                trackCustomEvent("sidebar_chat_expanded", {
                  from_state: "collapsed",
                  project_id: projectId
                });
                setOpen(true);
              }}
              iconClassName={ICON_SIZES.lg}
            />
          </div>
        )}

        {/* Chat Section - Fills remaining height when expanded */}
        {!isCollapsed && (
          <div className="flex-1 min-h-0 w-full">
            <ProjectChatSidebar
              projectId={projectId}
              embedded={true}
              chatWidth={chatWidth}
              setChatWidth={setChatWidth}
              isChatCollapsed={isChatCollapsed}
              setIsChatCollapsed={setIsChatCollapsed}
              selectedBusinessItem={selectedBusinessItem}
            />
          </div>
        )}
      </SidebarContent>

      <SidebarRail />

      {/* Resize Handle */}
      {resizeHandle && (
        <ResizeHandle
          onMouseDown={resizeHandle.onMouseDown}
          isDragging={resizeHandle.isDragging}
        />
      )}
    </Sidebar>
    </>
  );
}
