"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON>Content,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { MessageCircle, Send, Columns2, Columns, Paperclip, Smile, Columns3 } from "lucide-react";
import { FormEvent, useState, useRef } from "react";
import { useSidebar } from "@/components/ui/sidebar";
import {
  ChatBubble,
  ChatBubbleMessage,
} from "./ui/chat-bubble";
import { ChatInput } from "./ui/chat-input";
import { ChatMessageList } from "./ui/chat-message-list";
import { Logo } from "./ui/logo";
import { ICON_SIZES } from "@/lib/constants";

interface ProjectChatSidebarProps {
  projectId: string;
  isCollapsed?: boolean;
  onCollapseChange?: (collapsed: boolean) => void;
  embedded?: boolean; // New prop to indicate if it's embedded in sidebar
  chatWidth?: '30%' | '50%';
  setChatWidth?: (width: '30%' | '50%') => void;
  isChatCollapsed?: boolean;
  setIsChatCollapsed?: (collapsed: boolean) => void;
  selectedBusinessItem?: any;
}

// Mock chat data
const mockMessages = [
  {
    id: 1,
    user: "Siift AI",
    avatar: "",
    message: "Welcome to your project! I'm here to help you manage tasks, answer questions, and provide insights.",
    timestamp: "9:00 AM",
    isCurrentUser: false,
  },
  {
    id: 2,
    user: "You",
    avatar: "",
    message: "Hey! I've finished the homepage design. Can you review it?",
    timestamp: "10:30 AM",
    isCurrentUser: true,
  },
  {
    id: 3,
    user: "Siift AI",
    avatar: "",
    message: "Looks great! The design follows modern UI principles. I can help you implement the responsive layout if needed.",
    timestamp: "10:32 AM",
    isCurrentUser: false,
  },
  {
    id: 4,
    user: "You",
    avatar: "",
    message: "Perfect! I'll work on the component library in parallel.",
    timestamp: "10:35 AM",
    isCurrentUser: true,
  },
  {
    id: 5,
    user: "Siift AI",
    avatar: "",
    message: "I notice you're making great progress on the responsive layout. Would you like me to suggest some best practices for mobile optimization?",
    timestamp: "10:45 AM",
    isCurrentUser: false,
  },
  {
    id: 6,
    user: "You",
    avatar: "",
    message: "Great progress everyone! Let's sync up tomorrow morning.",
    timestamp: "11:15 AM",
    isCurrentUser: true,
  },
  {
    id: 7,
    user: "Siift AI",
    avatar: "",
    message: "Sounds good! I should have the navigation component ready by then.",
    timestamp: "11:18 AM",
    isCurrentUser: false,
  },
  {
    id: 8,
    user: "You",
    avatar: "",
    message: "I'll prepare the test cases for the new features.",
    timestamp: "11:45 AM",
    isCurrentUser: true,
  },
];

export function ProjectChatSidebar({
  projectId,
  isCollapsed: externalIsCollapsed,
  onCollapseChange,
  embedded = false,
  chatWidth: externalChatWidth,
  setChatWidth: externalSetChatWidth,
  isChatCollapsed: externalIsChatCollapsed,
  setIsChatCollapsed: externalSetIsChatCollapsed,
  selectedBusinessItem,
}: ProjectChatSidebarProps) {
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState(mockMessages);
  const [internalIsCollapsed, setInternalIsCollapsed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [internalChatWidth, setInternalChatWidth] = useState<'30%' | '50%'>('30%');
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const { setOpen, state } = useSidebar();

  // Use external state if provided, otherwise use internal state
  const isCollapsed =
    externalIsCollapsed !== undefined
      ? externalIsCollapsed
      : internalIsCollapsed;
  const setIsCollapsed = onCollapseChange || setInternalIsCollapsed;

  // Chat is always expanded - no collapse functionality
  const isChatCollapsed = false;
  const setIsChatCollapsed = () => {}; // No-op function

  const chatWidth =
    externalChatWidth !== undefined
      ? externalChatWidth
      : internalChatWidth;
  const setChatWidth = externalSetChatWidth || setInternalChatWidth;

  const handleSendMessage = (e?: FormEvent) => {
    if (e) e.preventDefault();
    if (message.trim()) {
      // Add user message
      const userMessage = {
        id: messages.length + 1,
        user: "You",
        avatar: "",
        message: message.trim(),
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        isCurrentUser: true,
      };

      setMessages(prev => [...prev, userMessage]);
      setMessage("");
      setIsLoading(true);

      // Simulate AI response
      setTimeout(() => {
        const aiMessage = {
          id: messages.length + 2,
          user: "Siift AI",
          avatar: "",
          message: "Thanks for your message! I'm here to help with your project.",
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          isCurrentUser: false,
        };
        setMessages(prev => [...prev, aiMessage]);
        setIsLoading(false);
      }, 1000);
    }
  };

  const handleWidthToggle = () => {
    if (setChatWidth) {
      setChatWidth(chatWidth === '30%' ? '50%' : '30%');
    }
  };

  // Render embedded version for sidebar
  if (embedded) {
    // Don't show anything when sidebar is collapsed
    if (state === "collapsed") {
      return null;
    }

    // Always show expanded chat - no collapse functionality
    return (
      <div 
        className={`w-full h-full border-t border-border ${
          selectedBusinessItem ? 'bg-blue-50/50 dark:bg-blue-950/20' : 'bg-card/95'
        } backdrop-blur-sm flex flex-col transition-all duration-300`}
      >
        {/* Header */}
        <div className={`p-4 border-b border-border/50 ${
          selectedBusinessItem ? 'bg-blue-100/50 dark:bg-blue-900/30' : 'bg-muted/30'
        } flex items-center justify-between flex-shrink-0`}>
          <div className="flex items-center gap-3">
            <h3 className={`font-semibold text-base ${
              selectedBusinessItem ? 'text-blue-900 dark:text-blue-100' : 'text-foreground'
            }`}>
              {selectedBusinessItem ? `${selectedBusinessItem.title} Chat` : "Project Chat"}
            </h3>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              handleWidthToggle();
            }}
            className="h-8 w-8 p-0 hover:bg-primary/10 transition-all duration-200"
            title={`Switch to ${chatWidth === '30%' ? '50%' : '30%'} width`}
          >
            {chatWidth === '30%' ? <Columns3 className={ICON_SIZES.sm} /> : <Columns2 className={ICON_SIZES.sm} />}
          </Button>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-hidden w-full min-h-0 relative">
          
          <ChatMessageList className="w-full h-full">
            {messages.map((msg) => (
              <ChatBubble
                key={msg.id}
                variant={msg.isCurrentUser ? "sent" : "received"}
              >
                {!msg.isCurrentUser && (
                  <div className="h-8 w-8 shrink-0 bg-primary/10 rounded-lg flex items-center justify-center p-1">
                    <Logo size={20} />
                  </div>
                )}
                <ChatBubbleMessage
                  variant={msg.isCurrentUser ? "sent" : "received"}
                  className="rounded-xl p-4"
                >
                  <div className="text-sm leading-relaxed whitespace-pre-line">{msg.message}</div>
                </ChatBubbleMessage>
              </ChatBubble>
            ))}
          </ChatMessageList>
        </div>

        {/* Message Input */}
        <div className="p-3 border-t border-border/50 bg-muted/30 backdrop-blur-sm w-full flex-shrink-0">
          <form
            onSubmit={handleSendMessage}
            className="relative rounded-lg border bg-background focus-within:ring-1 focus-within:ring-ring p-1 w-full"
          >
            <ChatInput
              ref={inputRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type a message..."
              className="min-h-12 resize-none rounded-lg bg-background border-0 p-3 shadow-none focus-visible:ring-0 text-sm w-full"
              data-chat-input
            />
            <div className="flex items-center p-2 pt-0 justify-end">
              <Button
                type="submit"
                variant="ghost"
                size="icon"
                disabled={!message.trim() || isLoading}
                className="h-8 w-8 p-0 hover:bg-primary/10 hover:text-primary border border-border/50 transition-all duration-200"
                title="Send message"
              >
                <Send className={ICON_SIZES.sm} />
              </Button>
            </div>
          </form>

          {/* Disclaimer */}
          <div className="mt-2 px-2">
            <p className="text-xs text-center opacity-50">
              Siift can make mistakes. Please double check answers to ensure accuracy.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Original fixed position version
  return (
    <div
      className={`${
        isCollapsed ? "w-12" : "w-[480px]"
      } border-l-2 border-border bg-card/95 backdrop-blur-sm shadow-lg flex flex-col fixed right-0 top-0 bottom-0 h-screen transition-all duration-300`}
    >
      {/* Header */}
      <div
        className={`${
          isCollapsed ? "h-16 px-2" : "p-4"
        } border-b border-border/50 bg-muted/30 flex items-center`}
      >
        <div className="flex items-center justify-between w-full">
          {!isCollapsed && (
            <div className="flex items-center gap-3">
              <h3 className="font-semibold text-foreground text-lg">
                {selectedBusinessItem ? `${selectedBusinessItem.title} Chat` : "Project Chat"}
              </h3>
            </div>
          )}
          {isCollapsed ? (
            <div className="w-full flex justify-center">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsCollapsed(!isCollapsed)}
                    className="h-12 w-12 p-0 hover:bg-primary/10 hover:text-primary"
                  >
                    <MessageCircle className={ICON_SIZES.lg} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="left">
                  <p>Open Chat</p>
                </TooltipContent>
              </Tooltip>
            </div>
          ) : (
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setIsCollapsed(!isCollapsed);
              }}
              className="h-8 w-8 p-0 hover:bg-primary/10 hover:text-primary"
            >
              <svg
                className={ICON_SIZES.sm}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </Button>
          )}
        </div>
      </div>

      {/* Messages */}
      {!isCollapsed && (
        <div className="flex-1 overflow-hidden relative">
          
          <ChatMessageList className="p-4">
            {messages.map((msg) => (
              <ChatBubble
                key={msg.id}
                variant={msg.isCurrentUser ? "sent" : "received"}
              >
                {!msg.isCurrentUser && (
                  <div className="h-10 w-10 shrink-0 bg-primary/10 rounded-lg flex items-center justify-center p-1.5">
                    <Logo size={28} />
                  </div>
                )}
                <ChatBubbleMessage
                  variant={msg.isCurrentUser ? "sent" : "received"}
                  className="rounded-xl p-4"
                >
                  <div className="flex items-center gap-2 mb-2">
                    {msg.isCurrentUser && (
                      <span className="text-base font-semibold">{msg.user}</span>
                    )}
                    <span className="text-sm text-muted-foreground">
                      {msg.timestamp}
                    </span>
                  </div>
                  <div className="text-base leading-relaxed whitespace-pre-line">{msg.message}</div>
                </ChatBubbleMessage>
              </ChatBubble>
            ))}

            {isLoading && (
              <ChatBubble variant="received">
                <div className="h-10 w-10 shrink-0 bg-primary/10 rounded-lg flex items-center justify-center p-1.5">
                  <Logo size={28} />
                </div>
                <ChatBubbleMessage isLoading />
              </ChatBubble>
            )}
          </ChatMessageList>
        </div>
      )}

      {/* Message Input - Fixed at bottom */}
      {!isCollapsed && (
        <div className="p-4 border-t border-border/50 bg-muted/30 backdrop-blur-sm">
          <form
            onSubmit={handleSendMessage}
            className="relative rounded-lg border bg-background focus-within:ring-1 focus-within:ring-ring p-1"
          >
            <ChatInput
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type a message..."
              className="min-h-16 resize-none rounded-lg bg-background border-0 p-4 shadow-none focus-visible:ring-0 text-base"
            />
            <div className="flex items-center p-3 pt-0 justify-between">
              <div className="flex gap-1">
                <Button
                  variant="ghost"
                  size="icon"
                  type="button"
                  className="h-8 w-8 p-0 hover:bg-primary/10"
                >
                  <Paperclip className={ICON_SIZES.sm} />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  type="button"
                  className="h-8 w-8 p-0 hover:bg-primary/10"
                >
                  <Smile className={ICON_SIZES.sm} />
                </Button>
              </div>
              <Button
                type="submit"
                variant="ghost"
                size="icon"
                disabled={!message.trim()}
                className="h-8 w-8 p-0 hover:bg-primary/10 hover:text-primary border border-border/50 transition-all duration-200"
                title="Send message"
              >
                <Send className={ICON_SIZES.sm} />
              </Button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}
