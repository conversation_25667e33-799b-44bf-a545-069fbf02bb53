"use client";
import { motion } from "motion/react";
import React from "react";
import { BackgroundBeams } from "./background-beams";
import { Button } from "./button";
import { Input } from "./input";

export const WaitlistSection = () => {
  const [email, setEmail] = React.useState("");
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsSubmitting(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Here you would typically send the email to your backend
    console.log("Email submitted:", email);

    setEmail("");
    setIsSubmitting(false);
  };

  return (
    <div className="h-[40rem] w-full rounded-md bg-background relative flex flex-col items-center justify-center antialiased overflow-hidden">
      <div className="max-w-2xl mx-auto p-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.1, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
        >
          <h1 className="relative z-10 text-lg md:text-7xl bg-clip-text text-transparent bg-gradient-to-b from-foreground to-muted-foreground text-center font-sans font-bold">
            Join the waitlist
          </h1>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
        >
          <p className="text-muted-foreground max-w-lg mx-auto my-2 text-sm text-center relative z-10">
            Be among the first to experience the future of AI-powered project
            management. Get early access to exclusive features, priority
            support, and special launch pricing.
          </p>
        </motion.div>

        <motion.form
          onSubmit={handleSubmit}
          className="relative mt-6 w-full"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
        >
          <Input
            type="email"
            placeholder="Enter your email address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full pr-32 relative z-10 py-6"
            required
          />
          <Button
            type="submit"
            disabled={isSubmitting || !email}
            className="absolute right-2 top-1/2 -translate-y-1/2 z-20"
            size="sm"
          >
            {isSubmitting ? "Joining..." : "Join Waitlist"}
          </Button>
        </motion.form>

        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
          className="mt-4 text-center"
        >
          <p className="text-xs text-muted-foreground relative z-10">
            Join 2,000+ developers already on the waitlist
          </p>
        </motion.div>
      </div>
      <BackgroundBeams />
    </div>
  );
};
