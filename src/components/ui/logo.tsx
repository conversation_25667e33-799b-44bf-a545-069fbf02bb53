"use client";

import React from "react";

interface LogoProps {
  size?: number; // px
  className?: string;
  animated?: boolean;
}

export const Logo: React.FC<LogoProps> = ({
  size = 48,
  className = "",
  animated = false,
}) => {
  // Sizes
  const greenSize = size * 0.7;
  const whiteSize = size * 0.38;
  // Orbit radii
  const greenOrbit = (size - greenSize) / 2;
  const whiteOrbit = (greenSize - whiteSize) / 2;

  return (
    <div
      className={className}
      style={{
        position: "relative",
        width: size,
        height: size,
        display: "inline-block",
        transform: !animated ? "rotate(45deg)" : undefined,
      }}
    >
      {/* Outer light green circle */}
      <div
        style={{
          position: "absolute",
          width: size,
          height: size,
          borderRadius: "50%",
          background: "#CEFFC5",
          left: 0,
          top: 0,
          zIndex: 1,
          overflow: "visible",
        }}
      >
        {/* Green orbit group */}
        <div
          style={{
            position: "absolute",
            width: size,
            height: size,
            left: 0,
            top: 0,
            zIndex: 2,
            pointerEvents: "none",
            animation: animated
              ? `logo-rotate-cw 1s linear infinite`
              : undefined,
            transform: !animated ? "rotate(290deg)" : undefined,
            transformOrigin: "50% 50%",
          }}
        >
          {/* Dark green circle attached to edge */}
          <div
            style={{
              position: "absolute",
              width: greenSize,
              height: greenSize,
              borderRadius: "50%",
              background: "#0A4000",
              left: size / 1.5 - greenSize / 1.5,
              top: greenOrbit * 2,
              zIndex: 2,
              display: "flex",
              transform: !animated ? "rotate(340deg)" : undefined,
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {/* White orbit group */}
            <div
              style={{
                position: "absolute",
                width: greenSize,
                height: greenSize,
                left: 0,
                top: 0,
                zIndex: 3,
                pointerEvents: "none",
                animation: animated
                  ? `logo-rotate-ccw 2s linear infinite`
                  : undefined,
                transform: !animated ? "rotate(-45deg)" : undefined,
                transformOrigin: "50% 50%",
              }}
            >
              {/* White circle attached to edge */}
              <div
                style={{
                  position: "absolute",
                  width: whiteSize,
                  height: whiteSize,
                  borderRadius: "50%",
                  background: "#fff",
                  left: greenSize / 2 - whiteSize / 2,
                  top: whiteOrbit * 2,
                  zIndex: 4,
                }}
              />
            </div>
          </div>
        </div>
      </div>
      {animated && (
        <style>{`
          @keyframes logo-rotate-cw {
            100% { transform: rotate(360deg); }
          }
          @keyframes logo-rotate-ccw {
            100% { transform: rotate(-360deg); }
          }
        `}</style>
      )}
    </div>
  );
};
