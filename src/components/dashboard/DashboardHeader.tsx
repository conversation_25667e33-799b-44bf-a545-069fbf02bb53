"use client";

import React, { useEffect, useState } from "react";
import { MainNav } from "@/components/navigation/main-nav";
import { MobileNav } from "@/components/navigation/mobile-nav";
import { UserMenu } from "@/components/navigation/user-menu";
import { ThemeToggle } from "@/components/theme-toggle";
import { Logo } from "@/components/ui/logo";
import { motion, AnimatePresence } from "framer-motion";

interface DashboardHeaderProps {
  showStickyHeader?: boolean;
}

export function DashboardHeader({ showStickyHeader = true }: DashboardHeaderProps) {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    if (!showStickyHeader) return;

    const handleScroll = () => {
      // Show sticky header when scrolled past the hero section
      // Use a smaller threshold for better responsiveness
      const scrollThreshold = 200;
      setIsScrolled(window.scrollY > scrollThreshold);
    };

    // Add throttling for better performance
    let ticking = false;
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", throttledHandleScroll, { passive: true });
    return () => window.removeEventListener("scroll", throttledHandleScroll);
  }, [showStickyHeader]);

  return (
    <>
      {/* Main Header - Part of page flow */}
      <motion.header
        className="w-full relative z-20 bg-transparent"
        initial={{ opacity: 1 }}
        animate={{
          opacity: isScrolled ? 0.8 : 1,
          scale: isScrolled ? 0.98 : 1
        }}
        transition={{ duration: 0.2 }}
      >
        <div className="container mx-auto px-4 flex h-16 items-center justify-between">
          {/* Left: Logo and Navigation */}
          <div className="flex items-center">
            {/* Mobile Nav Button */}
            <div className="flex items-center mr-4 md:hidden">
              <MobileNav />
            </div>

            {/* Logo and App Title */}
            <div className="flex items-center gap-3">
              <Logo size={32} animated={false} />
              <span className="text-xl font-bold text-foreground">Siift</span>
            </div>

            {/* Desktop Navigation - Adjacent to logo */}
            <div className="hidden md:flex ml-6">
              <MainNav />
            </div>
          </div>

          {/* Right: Theme Toggle and User Menu */}
          <div className="flex items-center gap-3">
            <ThemeToggle />
            <UserMenu />
          </div>
        </div>
      </motion.header>

      {/* Sticky Header - Appears on scroll */}
      {showStickyHeader && (
        <AnimatePresence>
          {isScrolled && (
            <motion.header
              initial={{ y: -100, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -100, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border shadow-lg"
            >
              {/* Noise Texture Overlay for sticky header */}
              <div
                className="absolute inset-0 opacity-[0.02] dark:opacity-[0.04] pointer-events-none"
                style={{
                  backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
                  backgroundSize: "256px 256px",
                }}
              />
              
              <div className="container mx-auto px-4 flex h-16 items-center justify-between relative z-10">
                {/* Left: Logo and Navigation */}
                <div className="flex items-center">
                  {/* Mobile Nav Button */}
                  <div className="flex items-center mr-4 md:hidden">
                    <MobileNav />
                  </div>

                  {/* Logo and App Title */}
                  <div className="flex items-center gap-3">
                    <Logo size={32} animated={false} />
                    <span className="text-xl font-bold text-foreground">Siift</span>
                  </div>

                  {/* Desktop Navigation - Adjacent to logo */}
                  <div className="hidden md:flex ml-6">
                    <MainNav />
                  </div>
                </div>

                {/* Right: Theme Toggle and User Menu */}
                <div className="flex items-center gap-3">
                  <ThemeToggle />
                  <UserMenu />
                </div>
              </div>
            </motion.header>
          )}
        </AnimatePresence>
      )}
    </>
  );
}
